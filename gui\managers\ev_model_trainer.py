import sys
import os
import threading
import time
import queue
from pathlib import Path
from typing import Dict, Any, Callable, Optional, List
import numpy as np
import pandas as pd
import torch

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 导入模型相关模块
try:
    from ev_charging_prediction import (
        DataPreprocessor, SSAVMDGRU, SSA, ChargingDataset,
        calculate_metrics, save_results, vmd_decompose_series,
        get_device_info, DEVICE, find_optimal_sequence_length,
        find_optimal_vmd_k, get_global_managers
    )
    print("✅ 成功导入EV预测模型模块")
except ImportError as e:
    print(f"❌ 导入EV预测模型模块失败: {e}")
    # 创建占位符类以避免错误
    class DataPreprocessor:
        pass
    class SSAVMDGRU:
        pass
    class SSA:
        pass

from ..utils.realtime_sync_manager import get_sync_manager
from ..managers.results_manager import get_results_manager


class EVModelTrainer:
    """EV充电负荷预测模型训练管理器"""
    
    def __init__(self):
        self.device = DEVICE if 'DEVICE' in globals() else torch.device('cpu')
        self.is_training = False
        self.training_thread = None
        self.stop_training_flag = False
        self.current_progress = 0.0
        self.training_status = "未开始"
        
        # 训练配置
        self.training_config = {
            'sequence_length': 24,
            'vmd_k': 5,
            'hidden_size': 64,
            'num_layers': 2,
            'dropout': 0.2,
            'model_type': 'transformer_gru',
            'loss_type': 'adaptive',
            'num_epochs': 100,
            'batch_size': 32,
            'learning_rate': 0.001,
            'patience': 10,
            'ssa_pop_size': 12,
            'ssa_max_iter': 10,
            'use_vmd': True,
            'use_ssa_optimization': True,
            'n_workers': 4
        }
        
        # 训练数据和结果
        self.training_data = None
        self.trained_model = None
        self.training_metrics = {}
        self.training_history = []
        
        # 回调函数
        self.progress_callback = None
        self.log_callback = None
        self.status_callback = None
        
        # 管理器
        self.sync_manager = None
        self.results_manager = None
        
        print(f"🎯 EV模型训练器已初始化，设备: {self.device}")
    
    def set_sync_manager(self, sync_manager):
        """设置同步管理器"""
        self.sync_manager = sync_manager
        print("✅ 同步管理器已设置")
    
    def set_callbacks(self, progress_callback=None, log_callback=None, status_callback=None):
        """设置回调函数"""
        self.progress_callback = progress_callback
        self.log_callback = log_callback
        self.status_callback = status_callback
        print("✅ 回调函数已设置")
    
    def update_config(self, config_updates: Dict[str, Any]):
        """更新训练配置"""
        self.training_config.update(config_updates)
        self._log(f"配置已更新: {config_updates}")
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前训练配置"""
        return self.training_config.copy()
    
    def load_data(self, data_path: str = None, dataframe: pd.DataFrame = None) -> bool:
        """加载训练数据"""
        try:
            self._log("开始加载数据...")
            self._update_status("加载数据中")
            
            # 初始化数据预处理器
            preprocessor = DataPreprocessor()
            
            if dataframe is not None:
                self._log("使用提供的DataFrame")
                df_cleaned = preprocessor.load_and_clean_data_from_df(dataframe)
            elif data_path and os.path.exists(data_path):
                self._log(f"从文件加载数据: {data_path}")
                df_cleaned = preprocessor.load_and_clean_data(data_path)
            else:
                # 尝试加载默认数据文件
                default_path = project_root / "charging_data.csv"
                if default_path.exists():
                    self._log(f"使用默认数据文件: {default_path}")
                    df_cleaned = preprocessor.load_and_clean_data(str(default_path))
                else:
                    self._log("❌ 未找到数据文件")
                    return False
            
            if df_cleaned is None or df_cleaned.empty:
                self._log("❌ 数据加载失败或为空")
                return False
            
            # 创建时间特征
            self._log("创建时间特征...")
            df_featured = preprocessor.create_time_features(df_cleaned)
            
            # 准备序列数据
            self._log("准备序列数据...")
            try:
                result = preprocessor.prepare_sequences(
                    df_featured, 
                    sequence_length=self.training_config['sequence_length']
                )
                
                # 确保返回值数量正确
                if len(result) == 4:
                    X, y, idx_arr, scaler = result
                elif len(result) == 3:
                    X, y, idx_arr = result
                    scaler = preprocessor.load_scaler  # 使用预处理器的scaler
                else:
                    self._log(f"❌ 序列准备返回值异常，期望3或4个值，实际得到{len(result)}个")
                    return False
                    
                if X is None or len(X) == 0:
                    self._log("❌ 序列数据准备失败")
                    return False
                    
            except Exception as e:
                self._log(f"❌ 序列数据准备过程出错: {str(e)}")
                return False
            
            # 保存训练数据
            self.training_data = {
                'X': X,
                'y': y,
                'idx_arr': idx_arr,
                'scaler': scaler,
                'df_featured': df_featured,
                'df_cleaned': df_cleaned,
                'preprocessor': preprocessor
            }
            
            self._log(f"✅ 数据加载成功，样本数量: {len(X)}")
            self._update_status("数据已加载")
            return True
            
        except Exception as e:
            self._log(f"❌ 数据加载失败: {str(e)}")
            self._update_status("数据加载失败")
            return False
    
    def optimize_hyperparameters(self) -> Dict[str, Any]:
        """自动优化超参数"""
        if not self.training_data:
            self._log("❌ 请先加载数据")
            return {}
        
        try:
            self._log("开始超参数优化...")
            self._update_status("优化超参数中")
            
            # 优化序列长度
            if self.training_config.get('optimize_sequence_length', False):
                self._log("优化序列长度...")
                optimal_seq_len = find_optimal_sequence_length(
                    self.training_data['df_featured'],
                    sequence_lengths=[12, 24, 48, 72],
                    test_size=0.2
                )
                self.training_config['sequence_length'] = optimal_seq_len
                self._log(f"最优序列长度: {optimal_seq_len}")
            
            # 优化VMD参数
            if self.training_config['use_vmd'] and self.training_config.get('optimize_vmd_k', False):
                self._log("优化VMD分解参数...")
                signal = self.training_data['df_cleaned']['Total Load'].values
                optimal_k = find_optimal_vmd_k(signal, max_k=8)
                self.training_config['vmd_k'] = optimal_k
                self._log(f"最优VMD K值: {optimal_k}")
            
            self._log("✅ 超参数优化完成")
            self._update_status("超参数优化完成")
            return self.training_config
            
        except Exception as e:
            self._log(f"❌ 超参数优化失败: {str(e)}")
            return {}
    
    def start_training(self) -> bool:
        """开始训练"""
        if self.is_training:
            self._log("⚠️ 训练已在进行中")
            return False
        
        if not self.training_data:
            self._log("❌ 请先加载数据")
            return False
        
        # 重置训练历史
        if hasattr(self, 'training_curve_history'):
            self.training_curve_history = []
        
        # 通过同步管理器重置GUI的训练历史
        if self.sync_manager:
            try:
                # 发送重置信号给GUI组件
                self.sync_manager.send_update_message('reset_training_history', {}, priority=2)
            except Exception as e:
                print(f"重置训练历史错误: {e}")
        
        self.stop_training_flag = False
        self.is_training = True
        self._update_status("准备训练")
        
        # 在新线程中开始训练
        self.training_thread = threading.Thread(
            target=self._training_worker,
            daemon=True
        )
        self.training_thread.start()
        
        return True
    
    def stop_training(self):
        """停止训练"""
        if self.is_training:
            self._log("正在停止训练...")
            self.stop_training_flag = True
            self._update_status("停止训练中")
    
    def _training_worker(self):
        """训练工作线程"""
        try:
            self._log("🚀 开始模型训练")
            self._update_status("训练中")
            self._update_progress(0.0, "初始化训练环境")
            
            # 准备训练数据
            success = self._prepare_training_data()
            if not success:
                return
            
            self._update_progress(10.0, "数据准备完成")
            
            # VMD分解（如果启用）
            if self.training_config['use_vmd']:
                success = self._perform_vmd_decomposition()
                if not success:
                    return
                self._update_progress(30.0, "VMD分解完成")
            
            # SSA优化（如果启用）
            if self.training_config['use_ssa_optimization']:
                success = self._perform_ssa_optimization()
                if not success:
                    return
                self._update_progress(50.0, "SSA优化完成")
            
            # 模型训练
            success = self._train_model()
            if not success:
                return
            
            # 模型评估
            success = self._evaluate_model()
            if not success:
                return
            
            self._update_progress(100.0, "训练完成")
            self._log("✅ 模型训练成功完成")
            self._update_status("训练完成")
            
        except Exception as e:
            self._log(f"❌ 训练过程出错: {str(e)}")
            self._update_status("训练失败")
        finally:
            self.is_training = False
    
    def _prepare_training_data(self) -> bool:
        """准备训练数据"""
        try:
            self._log("准备训练数据...")
            
            # 重新准备序列数据（如果序列长度改变了）
            if self.training_config['sequence_length'] != 24:  # 默认值
                preprocessor = self.training_data['preprocessor']
                try:
                    result = preprocessor.prepare_sequences(
                        self.training_data['df_featured'],
                        sequence_length=self.training_config['sequence_length']
                    )
                    
                    # 确保返回值数量正确
                    if len(result) == 4:
                        X, y, idx_arr, scaler = result
                    elif len(result) == 3:
                        X, y, idx_arr = result
                        scaler = preprocessor.load_scaler
                    else:
                        self._log(f"❌ 序列准备返回值异常，期望3或4个值，实际得到{len(result)}个")
                        return False
                    
                    self.training_data.update({
                        'X': X,
                        'y': y,
                        'idx_arr': idx_arr,
                        'scaler': scaler
                    })
                except Exception as e:
                    self._log(f"❌ 重新准备序列数据失败: {str(e)}")
                    return False
            
            # 数据分割
            preprocessor = self.training_data['preprocessor']
            try:
                # split_data返回3个元组：(X_train, y_train, idx_train), (X_val, y_val, idx_val), (X_test, y_test, idx_test)
                train_data, val_data, test_data = preprocessor.split_data(
                    self.training_data['X'],
                    self.training_data['y'],
                    self.training_data['idx_arr']
                )
                
                # 解包每个元组
                X_train, y_train, idx_train = train_data
                X_val, y_val, idx_val = val_data
                X_test, y_test, idx_test = test_data
                
                self.training_data.update({
                    'X_train': X_train,
                    'X_val': X_val,
                    'X_test': X_test,
                    'y_train': y_train,
                    'y_val': y_val,
                    'y_test': y_test,
                    'idx_train': idx_train,
                    'idx_val': idx_val,
                    'idx_test': idx_test
                })
                
            except ValueError as e:
                if "not enough values to unpack" in str(e):
                    self._log(f"❌ 数据分割解包错误: {str(e)}")
                    self._log("尝试兼容性处理...")
                    # 尝试不带idx的分割
                    try:
                        train_data, val_data, test_data = preprocessor.split_data(
                            self.training_data['X'],
                            self.training_data['y'],
                            None
                        )
                        X_train, y_train = train_data
                        X_val, y_val = val_data
                        X_test, y_test = test_data
                        
                        self.training_data.update({
                            'X_train': X_train,
                            'X_val': X_val,
                            'X_test': X_test,
                            'y_train': y_train,
                            'y_val': y_val,
                            'y_test': y_test
                        })
                        self._log("✅ 兼容性数据分割成功")
                    except Exception as e2:
                        self._log(f"❌ 兼容性数据分割也失败: {str(e2)}")
                        return False
                else:
                    self._log(f"❌ 数据分割过程出错: {str(e)}")
                    return False
            
            self._log(f"训练集大小: {len(X_train)}, 验证集: {len(X_val)}, 测试集: {len(X_test)}")
            return True
            
        except Exception as e:
            self._log(f"数据准备失败: {str(e)}")
            return False
    
    def _perform_vmd_decomposition(self) -> bool:
        """执行VMD分解"""
        try:
            self._log("执行VMD变分模态分解...")
            
            signal = self.training_data['y_train'].flatten()
            K = self.training_config['vmd_k']
            
            # VMD分解
            imfs = vmd_decompose_series(signal, K=K)
            
            self.training_data['vmd_imfs'] = imfs
            self._log(f"✅ VMD分解完成，分解为 {len(imfs)} 个模态")
            
            return True
            
        except Exception as e:
            self._log(f"VMD分解失败: {str(e)}")
            return False
    
    def _perform_ssa_optimization(self) -> bool:
        """执行SSA优化"""
        try:
            self._log("执行麻雀搜索算法优化...")
            
            # 创建数据加载器
            from torch.utils.data import DataLoader
            
            train_dataset = ChargingDataset(
                self.training_data['X_train'],
                self.training_data['y_train'],
                self.device
            )
            val_dataset = ChargingDataset(
                self.training_data['X_val'],
                self.training_data['y_val'],
                self.device
            )
            
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.training_config['batch_size'],
                shuffle=True,
                collate_fn=ChargingDataset.collate_fn
            )
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.training_config['batch_size'],
                shuffle=False,
                collate_fn=ChargingDataset.collate_fn
            )
            
            # SSA优化
            ssa = SSA(
                pop_size=self.training_config['ssa_pop_size'],
                max_iter=self.training_config['ssa_max_iter'],
                n_workers=self.training_config['n_workers'],
                log_callback=self._log
            )
            
            input_size = self.training_data['X_train'].shape[-1]
            output_size = 1
            
            best_params = ssa.optimize(train_loader, val_loader, input_size, output_size)
            
            # 更新配置
            if best_params:
                self.training_config.update({
                    'hidden_size': best_params.get('hidden_size', self.training_config['hidden_size']),
                    'num_layers': best_params.get('num_layers', self.training_config['num_layers']),
                    'dropout': best_params.get('dropout', self.training_config['dropout'])
                })
                
                self._log(f"✅ SSA优化完成，最优参数: {best_params}")
            
            return True
            
        except Exception as e:
            self._log(f"SSA优化失败: {str(e)}")
            return False
    
    def _train_model(self) -> bool:
        """训练模型"""
        try:
            self._log("开始模型训练...")
            
            # 创建模型
            input_size = self.training_data['X_train'].shape[-1]
            output_size = 1
            
            model = SSAVMDGRU(
                input_size=input_size,
                hidden_size=self.training_config['hidden_size'],
                num_layers=self.training_config['num_layers'],
                output_size=output_size,
                dropout=self.training_config['dropout'],
                loss_type=self.training_config['loss_type'],
                model_type=self.training_config['model_type']
            )
            
            # 创建数据加载器
            from torch.utils.data import DataLoader
            
            train_dataset = ChargingDataset(
                self.training_data['X_train'],
                self.training_data['y_train'],
                self.device
            )
            val_dataset = ChargingDataset(
                self.training_data['X_val'],
                self.training_data['y_val'],
                self.device
            )
            
            train_loader = DataLoader(
                train_dataset,
                batch_size=self.training_config['batch_size'],
                shuffle=True,
                collate_fn=ChargingDataset.collate_fn
            )
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.training_config['batch_size'],
                shuffle=False,
                collate_fn=ChargingDataset.collate_fn
            )
            
            # 设置模型保存路径
            model_save_path = project_root / "best_model.pth"
            
            # 训练模型
            training_history = model.train(
                train_loader=train_loader,
                val_loader=val_loader,
                num_epochs=self.training_config['num_epochs'],
                patience=self.training_config['patience'],
                model_save_path=str(model_save_path),
                progress_callback=self._training_progress_callback,
                log_callback=self._log
            )
            
            self.trained_model = model
            self.training_history = training_history
            
            self._log("✅ 模型训练完成")
            return True
            
        except Exception as e:
            self._log(f"模型训练失败: {str(e)}")
            return False
    
    def _evaluate_model(self) -> bool:
        """评估模型"""
        try:
            self._log("评估模型性能...")
            
            if not self.trained_model:
                self._log("❌ 没有训练好的模型")
                return False
            
            # 创建测试数据加载器
            from torch.utils.data import DataLoader
            
            test_dataset = ChargingDataset(
                self.training_data['X_test'],
                self.training_data['y_test'],
                self.device
            )
            test_loader = DataLoader(
                test_dataset,
                batch_size=self.training_config['batch_size'],
                shuffle=False,
                collate_fn=ChargingDataset.collate_fn
            )
            
            # 评估模型
            test_metrics = self.trained_model.evaluate(test_loader)
            
            # 生成预测
            predictions = self.trained_model.predict(self.training_data['X_test'])
            
            # 计算详细指标
            detailed_metrics = calculate_metrics(
                self.training_data['y_test'].flatten(),
                predictions.flatten()
            )
            
            self.training_metrics = {
                'test_loss': test_metrics,
                'detailed_metrics': detailed_metrics,
                'training_history': self.training_history
            }
            
            # 保存结果
            self.results_manager = get_results_manager()
            if self.results_manager:
                save_dir = self.results_manager.get_current_session_dir()
                save_results(
                    predictions=predictions,
                    y_test=self.training_data['y_test'],
                    metrics=detailed_metrics,
                    save_dir=save_dir,
                    load_scaler=self.training_data['scaler'],
                    results_manager=self.results_manager
                )
            
            self._log(f"✅ 模型评估完成，测试损失: {test_metrics:.4f}")
            return True
            
        except Exception as e:
            self._log(f"模型评估失败: {str(e)}")
            return False
    
    def _training_progress_callback(self, epoch, epochs, train_loss, val_loss):
        """训练进度回调"""
        progress = (epoch / epochs) * 50 + 50  # 训练部分占50%进度
        message = f"Epoch {epoch}/{epochs}, 训练损失: {train_loss:.4f}, 验证损失: {val_loss:.4f}"
        
        # 构建详细的训练数据（包含更多指标）
        epoch_data = {
            'epoch': epoch,
            'total_epochs': epochs,
            'train_loss': train_loss,
            'val_loss': val_loss,
            'progress': progress,
            # 计算基本指标
            'mae': abs(train_loss - val_loss),  # 简单的MAE近似
            'rmse': ((train_loss + val_loss) / 2) ** 0.5,  # 简单的RMSE近似
            'mape': abs((train_loss - val_loss) / max(val_loss, 0.001)) * 100 if val_loss != 0 else 0,  # MAPE近似
            'r2': max(0, 1 - (val_loss / max(train_loss, 0.001))) if train_loss != 0 else 0  # R²近似
        }
        
        # 存储训练历史
        if not hasattr(self, 'training_curve_history'):
            self.training_curve_history = []
        self.training_curve_history.append(epoch_data)
        
        # 更新进度
        self._update_progress(progress, message)
        
        # 更新回调
        if self.progress_callback:
            try:
                self.progress_callback(progress, message)
            except Exception as e:
                print(f"进度回调错误: {e}")
        
        # 同步到实时同步管理器（删除训练曲线同步）
        if self.sync_manager:
            # 只同步性能指标，不再同步训练曲线
            # self.sync_manager.sync_training_curve(epoch_data)  # 删除此行
            self.sync_manager.sync_metrics(epoch_data)  # 只同步指标
    
    def get_training_status(self) -> Dict[str, Any]:
        """获取训练状态"""
        return {
            'is_training': self.is_training,
            'progress': self.current_progress,
            'status': self.training_status,
            'has_data': self.training_data is not None,
            'has_model': self.trained_model is not None,
            'config': self.training_config.copy()
        }
    
    def get_training_metrics(self) -> Dict[str, Any]:
        """获取训练指标"""
        return self.training_metrics.copy() if self.training_metrics else {}
    
    def predict(self, X_input: np.ndarray) -> np.ndarray:
        """使用训练好的模型进行预测"""
        if not self.trained_model:
            raise ValueError("模型未训练，请先训练模型")
        
        return self.trained_model.predict(X_input)
    
    def save_model(self, save_path: str) -> bool:
        """保存训练好的模型（增强版）"""
        try:
            if not self.trained_model:
                self._log("❌ 没有训练好的模型可保存")
                return False

            # 获取模型输入尺寸
            input_size = None
            if self.training_data and 'X_train' in self.training_data:
                input_size = self.training_data['X_train'].shape[-1]

            # 构建完整的保存数据
            save_data = {
                'model_state_dict': self.trained_model.model.state_dict(),
                'config': self.training_config,
                'metrics': self.training_metrics,
                'scaler': self.training_data['scaler'] if self.training_data else None,
                'input_size': input_size,
                'model_version': '3.0.0',  # 版本标识
                'model_type': self.training_config.get('model_type', 'transformer_gru'),
                'save_timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                'device_info': str(self.device),
                'training_history': getattr(self, 'training_history', [])
            }

            torch.save(save_data, save_path)

            self._log(f"✅ 模型已保存到: {save_path}")
            self._log(f"   模型版本: {save_data['model_version']}")
            self._log(f"   输入尺寸: {input_size}")
            self._log(f"   模型类型: {save_data['model_type']}")
            return True

        except Exception as e:
            self._log(f"❌ 模型保存失败: {str(e)}")
            return False
    
    def load_model(self, load_path: str) -> bool:
        """加载训练好的模型（增强版）"""
        try:
            if not os.path.exists(load_path):
                self._log(f"❌ 模型文件不存在: {load_path}")
                return False

            self._log(f"🔄 开始加载模型: {load_path}")

            # 验证模型文件
            validation_result = self.validate_model_file(load_path)
            if not validation_result['valid']:
                self._log(f"❌ 模型文件验证失败: {validation_result['error']}")
                return False

            checkpoint = torch.load(load_path, map_location=self.device)

            # 检查模型版本兼容性
            model_version = checkpoint.get('model_version', '1.0.0')
            self._log(f"📋 模型版本: {model_version}")

            # 恢复配置
            loaded_config = checkpoint.get('config', {})
            self.training_config.update(loaded_config)
            self.training_metrics = checkpoint.get('metrics', {})

            # 获取输入尺寸
            input_size = checkpoint.get('input_size')
            if input_size is None:
                self._log("⚠️ 模型文件中缺少input_size信息，尝试从配置推断")
                input_size = self.training_config.get('input_size', 10)

            self._log(f"📐 输入尺寸: {input_size}")
            self._log(f"🏗️ 模型类型: {self.training_config.get('model_type', 'transformer_gru')}")

            # 重新创建模型
            self.trained_model = SSAVMDGRU(
                input_size=input_size,
                hidden_size=self.training_config['hidden_size'],
                num_layers=self.training_config['num_layers'],
                output_size=1,
                dropout=self.training_config['dropout'],
                loss_type=self.training_config['loss_type'],
                model_type=self.training_config['model_type']
            )

            # 加载模型权重
            self.trained_model.model.load_state_dict(checkpoint['model_state_dict'])

            # 恢复训练历史（如果有）
            if 'training_history' in checkpoint:
                self.training_history = checkpoint['training_history']

            # 恢复scaler（如果有）
            if 'scaler' in checkpoint and checkpoint['scaler'] is not None:
                if not self.training_data:
                    self.training_data = {}
                self.training_data['scaler'] = checkpoint['scaler']
                self._log("✅ 数据缩放器已恢复")

            self._log(f"✅ 模型加载成功")
            self._log(f"   保存时间: {checkpoint.get('save_timestamp', '未知')}")
            self._log(f"   原始设备: {checkpoint.get('device_info', '未知')}")

            return True

        except Exception as e:
            self._log(f"❌ 模型加载失败: {str(e)}")
            import traceback
            self._log(f"详细错误: {traceback.format_exc()}")
            return False

    def validate_model_file(self, model_path: str) -> Dict[str, Any]:
        """验证模型文件的有效性"""
        try:
            if not os.path.exists(model_path):
                return {'valid': False, 'error': '文件不存在'}

            # 检查文件扩展名
            if not model_path.lower().endswith('.pth'):
                return {'valid': False, 'error': '不支持的文件格式，请选择.pth文件'}

            # 尝试加载文件头部信息
            try:
                checkpoint = torch.load(model_path, map_location='cpu')
            except Exception as e:
                return {'valid': False, 'error': f'文件损坏或格式错误: {str(e)}'}

            # 检查必要字段
            required_fields = ['model_state_dict']
            missing_fields = [field for field in required_fields if field not in checkpoint]
            if missing_fields:
                return {'valid': False, 'error': f'缺少必要字段: {missing_fields}'}

            # 检查模型版本兼容性
            model_version = checkpoint.get('model_version', '1.0.0')
            supported_versions = ['1.0.0', '2.0.0', '3.0.0']
            if model_version not in supported_versions:
                return {'valid': False, 'error': f'不支持的模型版本: {model_version}'}

            return {'valid': True, 'error': None}

        except Exception as e:
            return {'valid': False, 'error': f'验证过程出错: {str(e)}'}

    def get_model_info(self, model_path: str) -> Dict[str, Any]:
        """获取模型文件信息（不加载模型）"""
        try:
            if not os.path.exists(model_path):
                return {'error': '文件不存在'}

            checkpoint = torch.load(model_path, map_location='cpu')

            info = {
                'file_path': model_path,
                'file_size': f"{os.path.getsize(model_path) / (1024*1024):.2f} MB",
                'model_version': checkpoint.get('model_version', '未知'),
                'model_type': checkpoint.get('model_type', '未知'),
                'input_size': checkpoint.get('input_size', '未知'),
                'save_timestamp': checkpoint.get('save_timestamp', '未知'),
                'device_info': checkpoint.get('device_info', '未知'),
                'config': checkpoint.get('config', {}),
                'metrics': checkpoint.get('metrics', {}),
                'has_scaler': 'scaler' in checkpoint and checkpoint['scaler'] is not None,
                'has_training_history': 'training_history' in checkpoint
            }

            return info

        except Exception as e:
            return {'error': f'获取模型信息失败: {str(e)}'}

    def _log(self, message: str):
        """记录日志"""
        timestamp = time.strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] [EV训练器] {message}"
        
        print(formatted_message)
        
        if self.log_callback:
            try:
                self.log_callback(message)
            except Exception as e:
                print(f"日志回调错误: {e}")
        
        # 通过同步管理器发送日志
        if self.sync_manager:
            try:
                self.sync_manager.sync_log_output(message, "TRAINING")
            except Exception as e:
                print(f"同步管理器日志错误: {e}")
    
    def _update_progress(self, progress: float, message: str = ""):
        """更新进度"""
        self.current_progress = progress
        
        if self.progress_callback:
            try:
                self.progress_callback(progress, message)
            except Exception as e:
                print(f"进度回调错误: {e}")
        
        # 通过同步管理器发送进度更新
        if self.sync_manager:
            try:
                self.sync_manager.sync_training_progress(progress, message)
            except Exception as e:
                print(f"同步管理器进度错误: {e}")
    
    def _update_status(self, status: str):
        """更新状态"""
        self.training_status = status
        
        if self.status_callback:
            try:
                self.status_callback(status)
            except Exception as e:
                print(f"状态回调错误: {e}")
        
        # 通过同步管理器发送状态更新
        if self.sync_manager:
            try:
                self.sync_manager.sync_model_state(status)
            except Exception as e:
                print(f"同步管理器状态错误: {e}")


# 全局EV模型训练器实例
_ev_trainer = None

def get_ev_trainer() -> EVModelTrainer:
    """获取全局EV模型训练器实例"""
    global _ev_trainer
    if _ev_trainer is None:
        _ev_trainer = EVModelTrainer()
    return _ev_trainer 