2025-07-31 21:17:24,862 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 21:17:30,885 - __main__ - INFO - 导入GUI模块...
2025-07-31 21:17:31,130 - __main__ - INFO - 创建GUI应用...
2025-07-31 21:17:31,130 - __main__ - ERROR - GUI应用启动失败: EVChargingPredictionGUI.__init__() takes 1 positional argument but 2 were given
2025-07-31 21:17:31,130 - __main__ - ERROR - Traceback (most recent call last):
  File "d:\EV项目论文\模型42-GUI-\run_gui.py", line 208, in main
    app = EVChargingPredictionGUI(root)
TypeError: EVChargingPredictionGUI.__init__() takes 1 positional argument but 2 were given

2025-07-31 21:25:32,113 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 21:25:32,377 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 21:25:32,377 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 21:25:38,567 - __main__ - INFO - 导入GUI模块...
2025-07-31 21:25:38,649 - __main__ - INFO - 创建GUI应用...
2025-07-31 21:25:39,098 - __main__ - ERROR - GUI应用启动失败: bad selectmode "single": must be none, browse, or extended
2025-07-31 21:25:39,102 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\EV项目论文\模型42-GUI-\run_gui.py", line 216, in main
    app = EVChargingPredictionGUI()
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\EV项目论文\模型42-GUI-\gui\main_app.py", line 54, in __init__
    self.create_main_content()
  File "D:\EV项目论文\模型42-GUI-\gui\main_app.py", line 183, in create_main_content
    self.parameter_panel = ParameterPanel(self.notebook, self)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\EV项目论文\模型42-GUI-\gui\components\parameter_panel.py", line 19, in __init__
    self.create_widgets()
  File "D:\EV项目论文\模型42-GUI-\gui\components\parameter_panel.py", line 35, in create_widgets
    self.create_parameter_tree(left_frame)
  File "D:\EV项目论文\模型42-GUI-\gui\components\parameter_panel.py", line 49, in create_parameter_tree
    self.param_tree = ttk.Treeview(tree_frame, selectmode='single')
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\anaconda3\Lib\tkinter\ttk.py", line 1180, in __init__
    Widget.__init__(self, master, "ttk::treeview", kw)
  File "C:\Users\<USER>\anaconda3\Lib\tkinter\ttk.py", line 527, in __init__
    tkinter.Widget.__init__(self, master, widgetname, kw=kw)
  File "C:\Users\<USER>\anaconda3\Lib\tkinter\__init__.py", line 2659, in __init__
    self.tk.call(
_tkinter.TclError: bad selectmode "single": must be none, browse, or extended

2025-07-31 21:28:51,120 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 21:28:51,426 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 21:28:51,426 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 21:28:57,757 - __main__ - INFO - 导入GUI模块...
2025-07-31 21:28:57,853 - __main__ - INFO - 创建GUI应用...
2025-07-31 21:29:10,180 - __main__ - ERROR - GUI应用启动失败: 'ThreadSafeQueue' object has no attribute 'get_nowait'
2025-07-31 21:29:10,181 - __main__ - ERROR - Traceback (most recent call last):
  File "D:\EV项目论文\模型42-GUI-\run_gui.py", line 216, in main
    app = EVChargingPredictionGUI()
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\EV项目论文\模型42-GUI-\gui\main_app.py", line 61, in __init__
    self.process_messages()
  File "D:\EV项目论文\模型42-GUI-\gui\main_app.py", line 250, in process_messages
    message = self.message_queue.get_nowait()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ThreadSafeQueue' object has no attribute 'get_nowait'

2025-07-31 21:29:57,255 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 21:29:57,588 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 21:29:57,588 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 21:30:03,954 - __main__ - INFO - 导入GUI模块...
2025-07-31 21:30:04,038 - __main__ - INFO - 创建GUI应用...
2025-07-31 21:30:07,334 - __main__ - INFO - GUI应用启动成功
2025-07-31 21:30:48,389 - __main__ - INFO - 用户退出系统
2025-07-31 21:30:48,627 - __main__ - INFO - GUI应用正常退出
2025-07-31 21:36:16,258 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 21:36:16,483 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 21:36:16,483 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 21:36:22,355 - __main__ - INFO - 导入GUI模块...
2025-07-31 21:36:22,440 - __main__ - INFO - 创建GUI应用...
2025-07-31 21:36:26,538 - __main__ - INFO - GUI应用启动成功
2025-07-31 21:36:42,178 - __main__ - INFO - 用户退出系统
2025-07-31 21:36:42,363 - __main__ - INFO - GUI应用正常退出
2025-07-31 21:37:09,693 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 21:37:15,386 - __main__ - INFO - 导入GUI模块...
2025-07-31 21:37:15,555 - __main__ - INFO - 创建GUI应用...
2025-07-31 21:37:17,141 - __main__ - INFO - GUI应用启动成功
2025-07-31 21:43:09,097 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 21:43:09,329 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 21:43:09,329 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 21:43:15,250 - __main__ - INFO - 导入GUI模块...
2025-07-31 21:43:15,363 - __main__ - INFO - 创建GUI应用...
2025-07-31 21:43:22,636 - __main__ - INFO - GUI应用启动成功
2025-07-31 21:44:24,442 - __main__ - INFO - 用户退出系统
2025-07-31 21:47:12,322 - __main__ - INFO - 用户退出系统
2025-07-31 21:47:12,492 - __main__ - INFO - GUI应用正常退出
2025-07-31 21:47:22,580 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 21:47:29,009 - __main__ - INFO - 导入GUI模块...
2025-07-31 21:47:29,193 - __main__ - INFO - 创建GUI应用...
2025-07-31 21:47:32,783 - __main__ - INFO - GUI应用启动成功
2025-07-31 21:48:35,906 - __main__ - INFO - 用户退出系统
2025-07-31 21:59:06,726 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 21:59:06,975 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 21:59:06,975 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 21:59:12,995 - __main__ - INFO - 导入GUI模块...
2025-07-31 21:59:13,086 - __main__ - INFO - 创建GUI应用...
2025-07-31 21:59:16,116 - __main__ - INFO - GUI应用启动成功
2025-07-31 22:00:31,961 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 22:00:37,622 - __main__ - INFO - 导入GUI模块...
2025-07-31 22:00:37,781 - __main__ - INFO - 创建GUI应用...
2025-07-31 22:00:39,285 - __main__ - INFO - GUI应用启动成功
2025-07-31 22:05:54,695 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 22:05:54,937 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 22:05:54,937 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 22:06:00,962 - __main__ - INFO - 导入GUI模块...
2025-07-31 22:06:01,054 - __main__ - INFO - 创建GUI应用...
2025-07-31 22:06:03,967 - __main__ - INFO - GUI应用启动成功
2025-07-31 22:06:58,640 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 22:07:04,419 - __main__ - INFO - 导入GUI模块...
2025-07-31 22:07:04,571 - __main__ - INFO - 创建GUI应用...
2025-07-31 22:07:06,024 - __main__ - INFO - GUI应用启动成功
2025-07-31 22:13:06,201 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 22:13:06,441 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 22:13:06,442 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 22:13:12,447 - __main__ - INFO - 导入GUI模块...
2025-07-31 22:13:12,523 - __main__ - INFO - 创建GUI应用...
2025-07-31 22:13:15,343 - __main__ - INFO - GUI应用启动成功
2025-07-31 22:14:23,307 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 22:14:29,029 - __main__ - INFO - 导入GUI模块...
2025-07-31 22:14:29,176 - __main__ - INFO - 创建GUI应用...
2025-07-31 22:14:30,639 - __main__ - INFO - GUI应用启动成功
2025-07-31 22:39:33,128 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-07-31 22:39:33,363 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-07-31 22:39:33,363 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-07-31 22:39:39,328 - __main__ - INFO - 导入GUI模块...
2025-07-31 22:39:39,404 - __main__ - INFO - 创建GUI应用...
2025-07-31 22:39:40,687 - __main__ - INFO - GUI应用启动成功
2025-07-31 22:40:20,079 - __main__ - INFO - 用户退出系统
2025-07-31 22:40:20,259 - __main__ - INFO - GUI应用正常退出
2025-08-01 09:06:36,947 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 09:06:42,974 - __main__ - INFO - 导入GUI模块...
2025-08-01 09:06:43,248 - __main__ - INFO - 创建GUI应用...
2025-08-01 09:06:44,746 - __main__ - INFO - GUI应用启动成功
2025-08-01 09:07:21,815 - __main__ - INFO - 用户退出系统
2025-08-01 09:09:49,225 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 09:09:49,490 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-01 09:09:49,490 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-01 09:09:55,605 - __main__ - INFO - 导入GUI模块...
2025-08-01 09:09:55,717 - __main__ - INFO - 创建GUI应用...
2025-08-01 09:09:56,971 - __main__ - INFO - GUI应用启动成功
2025-08-01 09:12:04,538 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 09:12:04,783 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-01 09:12:04,783 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-01 09:12:12,179 - __main__ - INFO - 导入GUI模块...
2025-08-01 09:12:12,256 - __main__ - INFO - 创建GUI应用...
2025-08-01 09:12:13,519 - __main__ - INFO - GUI应用启动成功
2025-08-01 09:12:48,295 - __main__ - INFO - 用户退出系统
2025-08-01 09:12:48,540 - __main__ - INFO - GUI应用正常退出
2025-08-01 09:13:10,601 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 09:13:16,399 - __main__ - INFO - 导入GUI模块...
2025-08-01 09:13:16,558 - __main__ - INFO - 创建GUI应用...
2025-08-01 09:13:17,968 - __main__ - INFO - GUI应用启动成功
2025-08-01 09:14:13,255 - __main__ - INFO - 用户退出系统
2025-08-01 09:35:26,419 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 09:35:32,250 - __main__ - INFO - 导入GUI模块...
2025-08-01 09:35:32,425 - __main__ - INFO - 创建GUI应用...
2025-08-01 09:35:33,940 - __main__ - INFO - GUI应用启动成功
2025-08-01 10:54:07,040 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 10:54:12,817 - __main__ - INFO - 导入GUI模块...
2025-08-01 10:54:13,016 - __main__ - INFO - 创建GUI应用...
2025-08-01 10:54:14,559 - __main__ - INFO - GUI应用启动成功
2025-08-01 10:59:48,093 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 10:59:53,849 - __main__ - INFO - 导入GUI模块...
2025-08-01 10:59:54,005 - __main__ - INFO - 创建GUI应用...
2025-08-01 10:59:55,437 - __main__ - INFO - GUI应用启动成功
2025-08-01 11:27:04,554 - __main__ - INFO - 用户退出系统
2025-08-01 11:29:27,769 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 11:29:33,592 - __main__ - INFO - 导入GUI模块...
2025-08-01 11:29:33,767 - __main__ - INFO - 创建GUI应用...
2025-08-01 11:29:35,339 - __main__ - INFO - GUI应用启动成功
2025-08-01 11:32:21,204 - __main__ - INFO - 用户退出系统
2025-08-01 15:24:11,596 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 15:24:17,581 - __main__ - INFO - 导入GUI模块...
2025-08-01 15:24:17,815 - __main__ - INFO - 创建GUI应用...
2025-08-01 15:24:19,268 - __main__ - INFO - GUI应用启动成功
2025-08-01 15:41:30,504 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 15:41:36,342 - __main__ - INFO - 导入GUI模块...
2025-08-01 15:41:36,515 - __main__ - INFO - 创建GUI应用...
2025-08-01 15:41:38,054 - __main__ - INFO - GUI应用启动成功
2025-08-01 15:57:23,610 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 15:57:29,398 - __main__ - INFO - 导入GUI模块...
2025-08-01 15:57:29,566 - __main__ - INFO - 创建GUI应用...
2025-08-01 15:57:31,204 - __main__ - INFO - GUI应用启动成功
2025-08-01 16:37:15,825 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 16:37:22,228 - __main__ - INFO - 导入GUI模块...
2025-08-01 16:37:22,429 - __main__ - INFO - 创建GUI应用...
2025-08-01 16:37:24,137 - __main__ - INFO - GUI应用启动成功
2025-08-01 16:38:44,056 - __main__ - INFO - 用户退出系统
2025-08-01 16:51:32,121 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 16:51:38,194 - __main__ - INFO - 导入GUI模块...
2025-08-01 16:51:38,373 - __main__ - INFO - 创建GUI应用...
2025-08-01 16:51:39,832 - __main__ - INFO - GUI应用启动成功
2025-08-01 16:59:25,266 - __main__ - INFO - 用户退出系统
2025-08-01 17:15:47,428 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 17:15:53,534 - __main__ - INFO - 导入GUI模块...
2025-08-01 17:15:53,633 - __main__ - INFO - 创建GUI应用...
2025-08-01 17:15:55,235 - __main__ - INFO - GUI应用启动成功
2025-08-01 17:21:34,249 - __main__ - INFO - 用户退出系统
2025-08-01 17:23:43,286 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 17:23:49,317 - __main__ - INFO - 导入GUI模块...
2025-08-01 17:23:49,427 - __main__ - INFO - 创建GUI应用...
2025-08-01 17:23:50,992 - __main__ - INFO - GUI应用启动成功
2025-08-01 17:24:52,152 - __main__ - INFO - 用户退出系统
2025-08-01 21:33:11,346 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 21:33:17,932 - __main__ - INFO - 导入GUI模块...
2025-08-01 21:33:18,043 - __main__ - INFO - 创建GUI应用...
2025-08-01 21:33:19,572 - __main__ - INFO - GUI应用启动成功
2025-08-01 21:53:00,793 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 21:53:07,084 - __main__ - INFO - 导入GUI模块...
2025-08-01 21:53:07,186 - __main__ - INFO - 创建GUI应用...
2025-08-01 21:53:08,760 - __main__ - INFO - GUI应用启动成功
2025-08-01 22:16:29,797 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 22:16:36,035 - __main__ - INFO - 导入GUI模块...
2025-08-01 22:16:36,132 - __main__ - INFO - 创建GUI应用...
2025-08-01 22:16:37,600 - __main__ - INFO - GUI应用启动成功
2025-08-01 22:18:35,463 - __main__ - INFO - 用户退出系统
2025-08-01 22:30:05,065 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-01 22:30:11,449 - __main__ - INFO - 导入GUI模块...
2025-08-01 22:30:11,567 - __main__ - INFO - 创建GUI应用...
2025-08-01 22:30:13,234 - __main__ - INFO - GUI应用启动成功
2025-08-02 10:10:30,256 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 10:10:38,426 - __main__ - INFO - 导入GUI模块...
2025-08-02 10:10:38,802 - __main__ - INFO - 创建GUI应用...
2025-08-02 10:10:40,212 - __main__ - INFO - GUI应用启动成功
2025-08-02 10:21:10,105 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 10:21:16,293 - __main__ - INFO - 导入GUI模块...
2025-08-02 10:21:16,402 - __main__ - INFO - 创建GUI应用...
2025-08-02 10:21:17,764 - __main__ - INFO - GUI应用启动成功
2025-08-02 10:28:08,348 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 10:28:14,543 - __main__ - INFO - 导入GUI模块...
2025-08-02 10:28:14,629 - __main__ - INFO - 创建GUI应用...
2025-08-02 10:28:15,997 - __main__ - INFO - GUI应用启动成功
2025-08-02 10:37:27,636 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 10:37:33,694 - __main__ - INFO - 导入GUI模块...
2025-08-02 10:37:33,780 - __main__ - INFO - 创建GUI应用...
2025-08-02 10:37:35,112 - __main__ - INFO - GUI应用启动成功
2025-08-02 10:39:14,288 - __main__ - INFO - 用户退出系统
2025-08-02 16:03:35,486 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 16:03:41,698 - __main__ - INFO - 导入GUI模块...
2025-08-02 16:03:41,787 - __main__ - INFO - 创建GUI应用...
2025-08-02 16:03:43,129 - __main__ - INFO - GUI应用启动成功
2025-08-02 16:21:22,142 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 16:21:28,209 - __main__ - INFO - 导入GUI模块...
2025-08-02 16:21:28,294 - __main__ - INFO - 创建GUI应用...
2025-08-02 16:21:29,629 - __main__ - INFO - GUI应用启动成功
2025-08-02 16:23:09,793 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 16:23:15,880 - __main__ - INFO - 导入GUI模块...
2025-08-02 16:23:15,957 - __main__ - INFO - 创建GUI应用...
2025-08-02 16:23:17,389 - __main__ - INFO - GUI应用启动成功
2025-08-02 16:29:44,706 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 16:29:44,954 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-02 16:29:44,954 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-02 16:29:51,113 - __main__ - INFO - 导入GUI模块...
2025-08-02 16:29:51,206 - __main__ - INFO - 创建GUI应用...
2025-08-02 16:29:53,173 - __main__ - INFO - GUI应用启动成功
2025-08-02 16:31:11,387 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 16:31:17,432 - __main__ - INFO - 导入GUI模块...
2025-08-02 16:31:17,518 - __main__ - INFO - 创建GUI应用...
2025-08-02 16:31:19,279 - __main__ - INFO - GUI应用启动成功
2025-08-02 16:40:02,091 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 16:40:02,337 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-02 16:40:02,337 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-02 16:40:08,424 - __main__ - INFO - 导入GUI模块...
2025-08-02 16:40:08,515 - __main__ - INFO - 创建GUI应用...
2025-08-02 16:40:09,869 - __main__ - INFO - GUI应用启动成功
2025-08-02 16:42:03,943 - __main__ - INFO - 用户退出系统
2025-08-02 16:42:04,213 - __main__ - INFO - GUI应用正常退出
2025-08-02 16:59:28,304 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 16:59:28,546 - numexpr.utils - INFO - Note: NumExpr detected 16 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.
2025-08-02 16:59:28,547 - numexpr.utils - INFO - NumExpr defaulting to 8 threads.
2025-08-02 16:59:34,679 - __main__ - INFO - 导入GUI模块...
2025-08-02 16:59:34,791 - __main__ - INFO - 创建GUI应用...
2025-08-02 16:59:35,843 - __main__ - INFO - GUI应用启动成功
2025-08-02 17:02:02,465 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 17:02:08,418 - __main__ - INFO - 导入GUI模块...
2025-08-02 17:02:08,521 - __main__ - INFO - 创建GUI应用...
2025-08-02 17:02:09,723 - __main__ - INFO - GUI应用启动成功
2025-08-02 17:02:35,136 - __main__ - INFO - 用户退出系统
2025-08-02 21:20:38,764 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 21:20:46,287 - __main__ - INFO - 导入GUI模块...
2025-08-02 21:20:46,399 - __main__ - INFO - 创建GUI应用...
2025-08-02 21:20:47,605 - __main__ - INFO - GUI应用启动成功
2025-08-02 21:22:23,349 - __main__ - INFO - 用户退出系统
2025-08-02 22:02:52,256 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-02 22:02:58,436 - __main__ - INFO - 导入GUI模块...
2025-08-02 22:02:58,513 - __main__ - INFO - 创建GUI应用...
2025-08-02 22:02:59,745 - __main__ - INFO - GUI应用启动成功
2025-08-02 22:03:14,562 - __main__ - INFO - 用户退出系统
2025-08-05 09:17:47,923 - __main__ - INFO - 启动电动汽车充电负荷预测系统GUI
2025-08-05 09:17:54,265 - __main__ - INFO - 导入GUI模块...
2025-08-05 09:17:54,352 - __main__ - INFO - 创建GUI应用...
2025-08-05 09:17:55,643 - __main__ - INFO - GUI应用启动成功
