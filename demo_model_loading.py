#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EV模型参数加载功能演示脚本
"""

import sys
import os
from pathlib import Path
import torch
import time

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_demo_models():
    """创建演示用的模型文件"""
    print("🎭 创建演示模型文件...")
    
    # 创建models目录
    models_dir = project_root / "models"
    models_dir.mkdir(exist_ok=True)
    
    # 创建不同类型的演示模型
    demo_models = [
        {
            'name': 'ev_model_basic_v1.pth',
            'description': '基础GRU模型 - 版本1.0',
            'config': {
                'model_type': 'gru',
                'hidden_size': 64,
                'num_layers': 2,
                'sequence_length': 24,
                'dropout': 0.2
            },
            'version': '1.0.0'
        },
        {
            'name': 'ev_model_transformer_v2.pth',
            'description': 'Transformer-GRU混合模型 - 版本2.0',
            'config': {
                'model_type': 'transformer_gru',
                'hidden_size': 128,
                'num_layers': 3,
                'sequence_length': 48,
                'dropout': 0.3
            },
            'version': '2.0.0'
        },
        {
            'name': 'ev_model_optimized_v3.pth',
            'description': '优化版模型 - 版本3.0（推荐）',
            'config': {
                'model_type': 'transformer_gru',
                'hidden_size': 256,
                'num_layers': 4,
                'sequence_length': 72,
                'dropout': 0.25,
                'use_vmd': True,
                'use_ssa_optimization': True
            },
            'version': '3.0.0'
        }
    ]
    
    created_files = []
    
    for model_info in demo_models:
        # 创建模型状态字典
        config = model_info['config']
        hidden_size = config['hidden_size']
        input_size = 15  # 假设输入特征数
        
        model_state = {
            'gru.weight_ih_l0': torch.randn(3 * hidden_size, input_size),
            'gru.weight_hh_l0': torch.randn(3 * hidden_size, hidden_size),
            'gru.bias_ih_l0': torch.randn(3 * hidden_size),
            'gru.bias_hh_l0': torch.randn(3 * hidden_size),
            'fc.0.weight': torch.randn(hidden_size, hidden_size * 2),
            'fc.0.bias': torch.randn(hidden_size),
            'fc.2.weight': torch.randn(1, hidden_size),
            'fc.2.bias': torch.randn(1)
        }
        
        # 创建完整的保存数据
        save_data = {
            'model_state_dict': model_state,
            'config': config,
            'input_size': input_size,
            'model_version': model_info['version'],
            'model_type': config['model_type'],
            'save_timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
            'device_info': 'cpu',
            'description': model_info['description'],
            'metrics': {
                'test_loss': 0.02 + torch.rand(1).item() * 0.01,
                'detailed_metrics': {
                    'mae': 0.015 + torch.rand(1).item() * 0.005,
                    'rmse': 0.02 + torch.rand(1).item() * 0.01,
                    'mape': 2.0 + torch.rand(1).item() * 1.0,
                    'r2': 0.95 + torch.rand(1).item() * 0.04
                }
            },
            'training_history': [
                {'epoch': i, 'train_loss': 0.1 - i*0.01, 'val_loss': 0.09 - i*0.008}
                for i in range(1, 11)
            ]
        }
        
        # 保存模型文件
        model_path = models_dir / model_info['name']
        torch.save(save_data, model_path)
        
        created_files.append({
            'path': model_path,
            'name': model_info['name'],
            'description': model_info['description'],
            'version': model_info['version'],
            'size': f"{model_path.stat().st_size / 1024:.2f} KB"
        })
        
        print(f"  ✅ {model_info['name']} - {model_info['description']}")
    
    return created_files

def display_demo_info(created_files):
    """显示演示信息"""
    print("\n" + "="*60)
    print("🎯 EV模型参数加载功能演示")
    print("="*60)
    
    print("\n📁 已创建的演示模型文件:")
    for file_info in created_files:
        print(f"  📄 {file_info['name']}")
        print(f"     描述: {file_info['description']}")
        print(f"     版本: {file_info['version']}")
        print(f"     大小: {file_info['size']}")
        print()
    
    print("🚀 使用步骤:")
    print("1. 启动GUI应用:")
    print("   python run_gui.py")
    print()
    print("2. 进入EV模型训练界面")
    print()
    print("3. 在'智能配置'区域点击'📂 加载模型参数'按钮")
    print()
    print("4. 选择上述任一模型文件进行测试:")
    for file_info in created_files:
        print(f"   • {file_info['name']} ({file_info['version']})")
    print()
    print("5. 查看模型信息确认对话框")
    print()
    print("6. 确认加载并观察:")
    print("   • 配置参数自动更新")
    print("   • 模型信息区域显示")
    print("   • 日志输出反馈")
    print()
    
    print("💡 测试建议:")
    print("• 先测试v3.0模型（推荐版本）")
    print("• 尝试加载不同版本的模型")
    print("• 观察配置参数的变化")
    print("• 检查模型信息显示是否正确")
    print()
    
    print("🔧 高级测试:")
    print("• 尝试加载损坏的文件（测试错误处理）")
    print("• 测试取消加载操作")
    print("• 观察异步加载的进度指示")

def main():
    """主演示函数"""
    print("🎭 EV模型参数加载功能演示")
    print("正在准备演示环境...")
    
    try:
        # 创建演示模型
        created_files = create_demo_models()
        
        # 显示演示信息
        display_demo_info(created_files)
        
        print("\n" + "="*60)
        print("✅ 演示环境准备完成！")
        print("现在可以启动GUI应用进行测试。")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 演示环境准备失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    main()
