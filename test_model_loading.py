#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型加载功能
"""

import sys
import os
from pathlib import Path
import torch
import numpy as np

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_model_file():
    """创建一个测试用的模型文件"""
    try:
        # 创建models目录
        models_dir = project_root / "models"
        models_dir.mkdir(exist_ok=True)
        
        # 创建一个简单的测试模型状态
        test_model_state = {
            'layer1.weight': torch.randn(64, 10),
            'layer1.bias': torch.randn(64),
            'layer2.weight': torch.randn(32, 64),
            'layer2.bias': torch.randn(32),
            'output.weight': torch.randn(1, 32),
            'output.bias': torch.randn(1)
        }
        
        # 创建完整的模型保存数据
        save_data = {
            'model_state_dict': test_model_state,
            'config': {
                'sequence_length': 24,
                'hidden_size': 64,
                'num_layers': 2,
                'dropout': 0.2,
                'model_type': 'transformer_gru',
                'loss_type': 'adaptive',
                'num_epochs': 100,
                'batch_size': 32,
                'learning_rate': 0.001,
                'patience': 10
            },
            'metrics': {
                'test_loss': 0.0234,
                'detailed_metrics': {
                    'mae': 0.0156,
                    'rmse': 0.0234,
                    'mape': 2.34,
                    'r2': 0.9876
                }
            },
            'input_size': 10,
            'model_version': '3.0.0',
            'model_type': 'transformer_gru',
            'save_timestamp': '2025-01-15 10:30:00',
            'device_info': 'cpu',
            'training_history': [
                {'epoch': 1, 'train_loss': 0.1, 'val_loss': 0.09},
                {'epoch': 2, 'train_loss': 0.08, 'val_loss': 0.07},
                {'epoch': 3, 'train_loss': 0.06, 'val_loss': 0.05}
            ]
        }
        
        # 保存测试模型
        test_model_path = models_dir / "test_ev_model.pth"
        torch.save(save_data, test_model_path)
        
        print(f"✅ 测试模型文件已创建: {test_model_path}")
        print(f"   文件大小: {test_model_path.stat().st_size / 1024:.2f} KB")
        
        return str(test_model_path)
        
    except Exception as e:
        print(f"❌ 创建测试模型文件失败: {e}")
        return None

def test_model_validation():
    """测试模型验证功能"""
    try:
        from gui.managers.ev_model_trainer import get_ev_trainer
        
        # 创建测试模型文件
        test_model_path = create_test_model_file()
        if not test_model_path:
            return False
        
        # 获取EV训练器
        ev_trainer = get_ev_trainer()
        
        # 测试模型验证
        print("\n🔍 测试模型文件验证...")
        validation_result = ev_trainer.validate_model_file(test_model_path)
        print(f"验证结果: {validation_result}")
        
        if validation_result['valid']:
            print("✅ 模型文件验证通过")
        else:
            print(f"❌ 模型文件验证失败: {validation_result['error']}")
            return False
        
        # 测试获取模型信息
        print("\n📋 测试获取模型信息...")
        model_info = ev_trainer.get_model_info(test_model_path)
        if 'error' in model_info:
            print(f"❌ 获取模型信息失败: {model_info['error']}")
            return False
        
        print("✅ 模型信息获取成功:")
        for key, value in model_info.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试模型验证功能失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试EV模型加载功能...")
    
    # 测试模型验证功能
    if test_model_validation():
        print("\n✅ 所有测试通过！")
        print("\n📝 使用说明:")
        print("1. 启动GUI应用: python run_gui.py")
        print("2. 在EV模型训练界面中点击'📂 加载模型参数'按钮")
        print("3. 选择刚创建的测试模型文件: models/test_ev_model.pth")
        print("4. 查看模型信息并确认加载")
        print("5. 观察配置参数是否正确更新")
    else:
        print("\n❌ 测试失败，请检查代码实现")

if __name__ == "__main__":
    main()
